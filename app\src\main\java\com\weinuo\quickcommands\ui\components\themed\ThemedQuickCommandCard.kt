package com.weinuo.quickcommands.ui.components.themed

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Image
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import android.graphics.BitmapFactory
import com.weinuo.quickcommands.model.QuickCommand
import com.weinuo.quickcommands.ui.theme.config.SwitchConfig
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.data.SettingsRepository

/**
 * 主题感知的快捷指令卡片组件
 *
 * 特点：
 * - 使用主题配置的样式，而非硬编码值
 * - 支持主题切换时的实时样式更新
 * - 保持与原QuickCommandCard完全相同的功能
 * - 根据主题自动调整圆角、间距、阴影等样式
 *
 * @param command 快捷指令数据
 * @param onClick 点击卡片时的回调
 * @param onLongClick 长按卡片时的回调
 * @param onEnabledChanged 启用状态变更的回调
 * @param isInSelectionMode 是否处于选择模式
 * @param isSelected 是否被选中
 * @param onSelectionToggle 切换选择状态的回调
 * @param modifier 修饰符
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ThemedQuickCommandCard(
    command: QuickCommand,
    onClick: (QuickCommand) -> Unit,
    onLongClick: () -> Unit,
    onEnabledChanged: (Boolean) -> Unit,
    isInSelectionMode: Boolean = false,
    isSelected: Boolean = false,
    onSelectionToggle: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val themeContext = LocalThemeContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }

    // 获取全局设置以使用动态字体大小（仅在天空蓝主题下使用）
    val settingsRepository = remember { SettingsRepository(context) }
    val globalSettings by settingsRepository.globalSettings.collectAsState()

    // 主题感知的字体样式配置
    val titleStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.titleMedium.copy(
            fontSize = globalSettings.cardTitleFontSize.sp,
            fontWeight = when (globalSettings.cardTitleFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.titleMedium
    }

    val contentStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的字体大小和字重
        MaterialTheme.typography.bodyMedium.copy(
            fontSize = globalSettings.cardContentFontSize.sp,
            fontWeight = when (globalSettings.cardContentFontWeight) {
                "normal" -> FontWeight.Normal
                "medium" -> FontWeight.Medium
                "bold" -> FontWeight.Bold
                else -> FontWeight.Medium
            }
        )
    } else {
        // 海洋蓝主题：使用原始固定样式
        MaterialTheme.typography.bodyMedium
    }

    val iconSize = if (themeManager.getCurrentThemeId() == "sky_blue") {
        // 天空蓝主题：使用全局设置的图标大小
        globalSettings.cardIconSize.dp
    } else {
        // 海洋蓝主题：使用MD3标准的头像尺寸（40dp符合Material Design 3规范）
        40.dp
    }

    // 根据主题获取样式配置
    val cornerRadius = cardStyle.defaultCornerRadius
    val paddingValues = cardStyle.getPaddingValues()
    val elevation = if (isSelected) cardStyle.selectedElevation else cardStyle.defaultElevation
    
    Card(
        modifier = modifier
            .fillMaxWidth()
            .combinedClickable(
                onClick = {
                    if (isInSelectionMode) {
                        onSelectionToggle()
                    } else {
                        onClick(command)
                    }
                },
                onLongClick = onLongClick
            ),
        shape = RoundedCornerShape(cornerRadius),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primaryContainer
            else
                MaterialTheme.colorScheme.surfaceContainerLow
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = elevation
        ),
        border = if (isSelected && cardStyle.selectedBorderWidth > 0.dp) {
            androidx.compose.foundation.BorderStroke(
                width = cardStyle.selectedBorderWidth,
                color = MaterialTheme.colorScheme.primary
            )
        } else null
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(paddingValues),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选择模式下显示复选框
            if (isInSelectionMode) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onSelectionToggle() },
                    modifier = Modifier.padding(end = cardStyle.contentHorizontalSpacing)
                )
            }

            // 图标显示（仅在有自定义图标时显示）
            if (command.hasCustomIcon && !command.iconUri.isNullOrEmpty()) {
                val bitmap = remember(command.iconUri) {
                    try {
                        BitmapFactory.decodeFile(command.iconUri)
                    } catch (e: Exception) {
                        null
                    }
                }

                // 只有成功加载图片时才显示图标
                if (bitmap != null) {
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = "指令图标",
                        modifier = Modifier
                            .size(iconSize)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop
                    )

                    Spacer(modifier = Modifier.width(cardStyle.contentHorizontalSpacing))
                }
            }

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(cardStyle.contentVerticalSpacing)
            ) {
                // 指令名称
                Text(
                    text = command.name,
                    style = titleStyle,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // 触发条件信息（仅在非天空蓝主题下显示）
                if (themeManager.getCurrentThemeId() != "sky_blue") {
                    Text(
                        text = if (command.triggerConditions.isEmpty()) {
                            "无条件"
                        } else if (command.triggerConditions.size == 1) {
                            command.triggerConditions.first().getDescription()
                        } else {
                            "${command.triggerConditions.first().getDescription()} 等"
                        },
                        style = contentStyle,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                // 执行任务信息
                Text(
                    text = if (command.tasks.isEmpty()) {
                        "无任务"
                    } else if (command.tasks.size == 1) {
                        command.tasks.first().getDescription()
                    } else {
                        "${command.tasks.first().getDescription()} 等"
                    },
                    style = contentStyle,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }

            // 启用/禁用开关 - 使用主题感知的开关组件
            themeContext.componentFactory.createSwitch()(
                SwitchConfig(
                    checked = command.isEnabled,
                    onCheckedChange = onEnabledChanged,
                    modifier = Modifier.padding(start = cardStyle.contentHorizontalSpacing + 4.dp)
                )
            )
        }
    }
}


